<script setup lang="ts">
import { computed, nextTick, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useToggle } from '@vueuse/core'
import { de } from 'element-plus/es/locale'
import StockDialog from './components/StockDialog.vue'
import AddDialog from './components/AddDialog.vue'
import EditDialog from './components/EditDialog.vue'
import AddNewDefectDialog from '@/pages/commonData/defectData/components/AddDialog.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import UploadFile from '@/components/UploadFile/index.vue'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import { debounce, getDefaultSaleSystem, getFilterData, orderStatusConfirmBox } from '@/common/util'
import { formatDate, formatHashTag, formatLengthMul, formatMeterDiv, formatMeterMul, formatRollDiv, formatTwoDecimalsDiv, formatWeightDiv } from '@/common/format'
import {
  AddOrUpdateFpmQualityCheck,
  GetFpmQualityCheckByStockId,
  addFpmQualityCheck,
  deleteFpmQualityCheckDefect,
  getFpmQualityCheckDefectList,
  getFpmQualityCheckList,
  getInfoBasicDefectlistEnum,
  updateFpmQualityCheck,
  updateFpmQualityCheckDefect,
} from '@/api/fpQualityCheck'
import { sumNum } from '@/util/tableFooterCount'
import ScanerInput from '@/components/ScanerInput/index.vue'
import { useGlobalData } from '@/stores/globalData'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import { processDataIn, processDataOut } from '@/common/handBinary'
import { readUpperNumberCommand, useMeasureMeterService } from '@/use/useMeasurMeterService'
import { useMeasureMeterStore } from '@/stores/measureMeter'
import { usePrintTemplate } from '@/components/PrintPopoverBtn/index'
import { addInfoBasicDefect } from '@/api/defectData'
import { GetDictionaryDetailEnumListApi } from '@/api/employees'
import { DictionaryType } from '@/common/enum'

const { options: printOptions } = usePrintTemplate({
  printType: PrintType.PrintTemplateQuarantine,
  dataType: PrintDataType.Product,
})

const filterData = reactive<any>({
  // quality_check_date: new Date(),
})

const form = reactive<any>({
  user: '',
  scanCode: '',
})
const state = reactive<any>({
  ciDianList: [],
  pid: 0,
  // 表单字段
  edge_width: '',
  defect_weight: '',
  qc_gram_weight: '',
  useful_width: '',
  actually_weight: '',
  remark: '',
})

// 疵点表单数据
const defectForm = reactive({
  defect_name: '',
  defect_code: '',
  defect_position: '0',
  defect_count: '0',
  score: 1,
  kind_id: null,
  measurement_unit_id: 0,
  measurement_unit_name: '',
  uploadFiles: [] as string[], // 上传的文件列表
})

// 疵点表单引用
const defectFormRef = ref()

// 疵点表单验证规则
const defectFormRules = computed(() => ({
  defect_name: [
    { required: true, message: '请输入疵点名称', trigger: 'blur' },
  ],
  defect_position: [
    { required: true, message: '请输入疵点位置', trigger: 'blur' },
  ],
  defect_count: [
    { required: true, message: '请输入疵点数量', trigger: 'blur' },
    { pattern: /^[1-9]\d*$/, message: '疵点数量必须为正整数', trigger: 'blur' },
  ],
  measurement_unit_id: [
    { required: isOther.value, message: '请选择单位名称', trigger: 'change' },
  ],
}))

// 编辑步骤状态：1-位置，2-个数，3-打分
const editStep = ref(1)

// 疵点列表加载状态
const defectListLoading = ref(true)

// 根据步骤显示不同的标题
const stepTitle = computed(() => {
  switch (editStep.value) {
    case 1: return '疵点位置'
    case 2: return '疵点个数'
    case 3: return '疵点打分'
    default: return '疵点位置'
  }
})

// 根据步骤显示不同的按钮文字
const stepButtonText = computed(() => {
  return editStep.value === 3 ? '确定' : '下一个'
})

// 分割器拖拽相关
const leftPanelWidth = ref(30) // 左侧面板宽度百分比
const isDragging = ref(false)
const startX = ref(0)
const startLeftWidth = ref(50)

// 不展示，不用响应式
let activeStockId = 0
const EditDialogRef = ref()
const tablesRef1 = ref()
const AddDialogRef = ref()
const AddNewDefectRef = ref()

// 滚动定位相关的ref
const scrollbarRef = ref()
const positionStepRef = ref()
const countStepRef = ref()
const scoreStepRef = ref()

const { fetchData: fetchDataList, data: mainList, total: totalList, page: pageList, size: sizeList, loading: loadingList, handleSizeChange, handleCurrentChange } = getFpmQualityCheckList()

const measureMeterStore = useMeasureMeterStore()
const isConnected = computed(() => {
  return measureMeterStore.measureMeterState.isConnected
})
const Log = computed(() => {
  return measureMeterStore.measureMeterState.Log
})
const finishProductionOptions = reactive({
  tableConfig: {
    fieldApiKey: 'fpQualityCheck_table_left',
    showSlotNums: true,
    loading: loadingList.value,
    showPagition: true,
    page: pageList,
    size: sizeList,
    total: totalList,
    showCheckBox: false,
    showOperate: true,
    operateWidth: '150',
    height: '520',
    showSort: false,
    handleSizeChange,
    handleCurrentChange,
    rowIndex: '',
    footerMethod: (val: any) => finishProductionOptions.FooterMethod(val),
    cellDBLClickEvent: (row: any) => {
      finishProductionOptions.tableConfig.rowIndex = row.rowIndex
      getCiDianTable(row.data[row.rowIndex].id)
    },
  },
  datalist: [],
  columnList: [
    {
      sortable: true,
      field: 'order_no',
      title: '单号',
      width: '120',
    },
    {
      sortable: true,
      field: 'order_type_name',
      title: '单据类型',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'quality_check_date',
      title: '质检日期',
      minWidth: 100,
      is_date: true,
    },
    {
      sortable: true,
      field: 'bar_code',
      title: '条形码',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'product_code',
      title: '成品编号',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'product_name',
      title: '成品名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'product_color_code',
      title: '色号',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'product_color_name',
      title: '颜色',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'dyelot_number',
      title: '缸号',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'roll',
      title: '条数',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'volume_number',
      title: '卷号',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'weight',
      title: '数量',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'edge_width',
      title: '包边门幅',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'useful_width',
      title: '有效门幅',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'qc_gram_weight',
      title: '克重',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'defect_weight',
      title: '次布数量',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'actually_weight',
      title: '实际数量',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'defect_merge_str',
      title: '疵点信息',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'remark',
      title: '质检备注',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'quality_checker_name',
      title: '质检员',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'creator_name',
      title: '创建人',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'create_time',
      title: '创建时间',
      minWidth: 150,
      isDate: true,
    },
    {
      sortable: true,
      field: 'update_user_name',
      title: '修改人',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'update_time',
      title: '修改时间',
      minWidth: 150,
      isDate: true,
    },
  ],
  FooterMethod: ({ columns, data }: any) => {
    return [
      columns.map((column: any, _columnIndex: number) => {
        if ([0].includes(_columnIndex))
          return ''

        // 条数 数量 次布数量
        if (['roll'].includes(column.field))
          return sumNum(data, 'roll', '', 'float')

        if (['weight'].includes(column.field))
          return sumNum(data, 'weight', '', 'float')

        if (['defect_weight'].includes(column.field))
          return sumNum(data, 'defect_weight', '', 'float')

        return null
      }),
    ]
  },
  edit: (row: any) => {
    EditDialogRef.value.showAddDialog(row)
  },
  handleSure: (item: any) => {
    const user = getDefaultSaleSystem()
    if (item?.length) {
      if (isConnected.value)
        measureMeterStore.measureMeterState.handleZero()
      form.product_code = item[0].product_code
      form.product_name = item[0].product_name
      form.product_color_code = item[0].product_color_code
      form.product_color_name = item[0].product_color_name
      form.dyelot_number = item[0].dyelot_number
      form.volume_number = item[0].volume_number
      form.stock_id = item[0].id
      form.user_name = user?.user_name
      form.user_id = user?.user_id
      form.finish_product_craft = item[0].finish_product_craft
      form.finish_product_width_and_unit_name = item[0].finish_product_width_and_unit_name
      form.finish_product_gram_weight_and_unit_name = item[0].finish_product_gram_weight_and_unit_name
      activeStockId = item[0].id

      // 带出条码
      form.scanCode = item[0].bar_code
      form.total_roll = formatRollDiv(item[0].total_roll)
      getFpmQualityCheckByStockIdFn(activeStockId)
    }
  },
})

const {
  fetchData: fetchDataList1,
  data: mainList1,
  total: totalList1,
  page: pageList1,
  size: sizeList1,
  loading: loadingList1,
  handleSizeChange: handleSizeChange1,
  handleCurrentChange: handleCurrentChange1,
} = getFpmQualityCheckDefectList()

const tableOptions = reactive({
  tableConfig: {
    fieldApiKey: 'fpQualityCheck_table_right',
    showPagition: true,
    showSlotNums: true,
    showCheckBox: false,
    showOperate: true,
    operateWidth: '90',
    height: '520',
    showSort: false,
    page: pageList1,
    size: sizeList1,
    total: totalList1,
    loading: loadingList1.value,
    handleSizeChange: handleSizeChange1,
    handleCurrentChange: handleCurrentChange1,
  },
  datalist: [],
  columnList: [
    {
      sortable: true,
      field: 'bar_code',
      title: '条形码',
      minWidth: 150,
    },
    {
      sortable: true,
      field: 'defect_name',
      title: '疵点名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'measurement_unit_name',
      title: '单位名称',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'defect_position',
      title: '疵点位置',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'defect_count',
      title: '疵点数量',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'score',
      title: '分数',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'fabric_inspector_name',
      title: '验布员',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'creator_name',
      title: '创建人',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'create_time',
      title: '创建时间',
      minWidth: 150,
      isDate: true,
    },
    {
      sortable: true,
      field: 'update_user_name',
      title: '修改人',
      minWidth: 100,
    },
    {
      sortable: true,
      field: 'update_time',
      title: '修改时间',
      minWidth: 150,
      isDate: true,
    },
  ],
  edit: (row: any) => {
    if (row.defect_id === 0) {
      editOtherDefect(row)
      return
    }
    editDefect(row)
  },
  delete: async (row: any) => {
    await orderStatusConfirmBox({
      id: row.id,
      audit_status: 1,
      message: { desc: '是否确认删除该条疵点', title: '删除提醒' },
      api: deleteFpmQualityCheckDefect,
      fn: () => {
        getCiDianTable(state.pid)
      },
    })
    // await deleteFetchData({ id: row.id })
    // if (deleteSuccess.value) {
    //   ElMessage.success('删除成功')
    // } else {
    //   ElMessage.error(deleteMsg.value)
    // }
    // getCiDianTable(state.pid)
  },
})

async function getCiDianTable(id: number) {
  defectListLoading.value = true
  try {
    await fetchDataList1({ pid: id })
    state.pid = id
    tableOptions.datalist = processDataOut(mainList1.value?.list || [])
  }
  finally {
    // 模拟加载时间，让用户能看到骨架屏效果
    setTimeout(() => {
      defectListLoading.value = false
    }, 500)
  }
}
const arrange_time = ref([new Date(), new Date()])

async function getData() {
  const query = {
    ...filterData,
    // quality_check_date: formatDate(filterData.quality_check_date),
  }
  if (arrange_time?.value?.length) {
    query.quality_check_date_begin = formatDate(arrange_time.value[0] as unknown as string)
    query.quality_check_date_end = formatDate(arrange_time.value[1] as unknown as string)
  }
  finishProductionOptions.tableConfig.rowIndex = ''
  await fetchDataList(getFilterData({ ...query }))
  if (state.pid) {
    const index = finishProductionOptions.datalist.findIndex((item: any) => item.id === state.pid)
    finishProductionOptions.tableConfig.rowIndex = String(index)
    getCiDianTable(state.pid)
  }
  else {
    tableOptions.datalist = []
  }
}

const { fetchData: fetchList, data: resList } = getInfoBasicDefectlistEnum()

async function init() {
  await fetchList()
  await loadDefectCategories()

  state.ciDianList = resList.value?.list || []
}
function handleConnectToSerialPort() {
  if (!isConnected.value) {
    const measureMeterService = useMeasureMeterService()
    try {
      measureMeterService.connectToSerialPort()
      measureMeterStore.setMeasureMeterState(measureMeterService)
    }
    catch (error) {
      console.error('串口连接错误:', error)
    }
  }
}
function handleZero() {
  measureMeterStore.measureMeterState.handleZero()
}
function handleDisconnectToSerialPort() {
  measureMeterStore.measureMeterState.clearLogMessages()
  measureMeterStore.clearLogList()
  measureMeterStore.measureMeterState.disconnectPort()
}
const showLog = ref(false)
function toggleLog() {
  showLog.value = !showLog.value
}
onMounted(() => {
  init()
  getData()
  // 初始化时显示骨架屏演示效果
  setTimeout(() => {
    defectListLoading.value = true
    setTimeout(() => {
      defectListLoading.value = false
    }, 2000)
  }, 1000)
})
watch(
  filterData,
  debounce(() => {
    getData()
  }, 400),
)

watch(
  () => mainList.value,
  () => {
    finishProductionOptions.datalist
      = mainList?.value?.list?.map((item: any) => {
        return {
          ...item,
          roll: formatTwoDecimalsDiv(item.roll),
          weight: formatWeightDiv(item.weight),
          defect_weight: formatWeightDiv(item.defect_weight),
          edge_width: formatTwoDecimalsDiv(item.edge_width),
          useful_width: formatTwoDecimalsDiv(item.useful_width),
          qc_gram_weight: formatWeightDiv(item.qc_gram_weight),
          actually_weight: formatWeightDiv(item.actually_weight),
        }
      }) || []
  },
  { deep: true },
)

const StockDialogRef = ref()
function showDialog() {
  StockDialogRef.value.state.showModal = true
}

const currentFrameData = computed(() => measureMeterStore.measureMeterState.currentFrameData)
watch(() => currentFrameData.value, (newVal) => {
  defectForm.defect_position = formatMeterDiv(newVal)
})
const [isOther, setIsOther] = useToggle(false)
function handleClick(row: any) {
  setIsOther(false)
  // AddDialogRef.value.showAddDialog(row, false, isAdd)
  defectForm.defect_name = row.name
  defectForm.measurement_unit_name = row.measurement_unit_name
  defectForm.measurement_unit_id = row.measurement_unit_id
  if (isConnected.value) {
    measureMeterStore.setCommand(readUpperNumberCommand)// 开始计时
    measureMeterStore.measureMeterState.resume()
    // 立即读取米数到input框
    defectForm.defect_position = formatMeterDiv(currentFrameData.value)
  }
}
function editDefect(row: any) {
  console.log('row', row)
  AddDialogRef.value.showAddDialog(row, false, false)
  if (isConnected.value) {
    measureMeterStore.setCommand(readUpperNumberCommand)// 开始计时
    measureMeterStore.measureMeterState.resume()
    AddDialogRef.value.state.form.defect_position = formatMeterDiv(currentFrameData.value)
  }
}
function editOtherDefect(row: any) {
  AddDialogRef.value.showAddDialog(row, true, false)
  if (isConnected.value) {
    measureMeterStore.setCommand(readUpperNumberCommand)// 开始计时
    measureMeterStore.measureMeterState.resume()
    AddDialogRef.value.state.form.defect_position = formatMeterDiv(currentFrameData.value)
  }
}
function handleOtherClick() {
  // AddDialogRef.value.showAddDialog(row, true, isAdd)
  setIsOther(true)
  defectForm.defect_name = ''
  defectForm.measurement_unit_name = ''
  defectForm.measurement_unit_id = null
  if (isConnected.value) {
    // 开始计时
    measureMeterStore.measureMeterState.resume()
    measureMeterStore.setCommand(readUpperNumberCommand)
    // 立即读取米数到input框
    defectForm.defect_position = formatMeterDiv(currentFrameData.value)
  }
}
function handleAddDialogHide() {
  measureMeterStore.measureMeterState.pause()
}
const { fetchData: AddfetchData, data: AddData, success: addSuccess, msg: addMsg } = addFpmQualityCheck()
const { fetchData: updatefetchData, success: updateSuccess, msg: updateMsg } = updateFpmQualityCheckDefect()
// 添加疵点
async function defectHandleSure(defect_form: any) {
  if (defect_form.id) {
    await updatefetchData(defect_form)
    if (!updateSuccess.value)
      return ElMessage.error(updateMsg.value)

    ElMessage.success('提交成功')
    await getData()
    await getCiDianTable(defect_form.pid)
  }
}
const isSyncToDefectData = ref(false)
async function saveNewDefect() {
  // 表单验证
  if (!defectFormRef.value)
    return ElMessage.error('表单引用未找到')

  try {
    await defectFormRef.value.validate()
  }
  catch (error) {
    return ElMessage.error('请完善表单信息')
  }

  if (!form.stock_id && !defectForm.stock_id)
    return ElMessage.error('还未扫码或者从库存中添加数据')

  const formData = {
    stock_id: form.stock_id || defectForm.stock_id,
    quality_check_date: formatDate(new Date() as unknown as string),
    defect_item: [{ ...defectForm, defect_count: Number(defectForm.defect_count), defect_position: formatLengthMul(defectForm.defect_position) }],
  }
  await AddfetchData(formData)
  if (!addSuccess.value)
    return ElMessage.error(addMsg.value)
  if (isOther.value && isSyncToDefectData.value) {
    await newDefectHandleSure({
      code: defectForm.defect_code,
      measurement_unit_id: defectForm.measurement_unit_id,
      name: defectForm.defect_name,
      sort: 0,
      remark: '',
      kind_id: defectForm.kind_id,
    })
  }
  ElMessage.success('提交成功')

  // 重置表单
  resetDefectForm()

  await getData()
  await getCiDianTable(AddData.value?.id)
}

// 重置疵点表单
function resetDefectForm() {
  if (defectFormRef.value)
    defectFormRef.value.resetFields()

  // 重置表单数据
  Object.assign(defectForm, {
    defect_name: '',
    defect_code: '',
    defect_position: '0',
    defect_count: '0',
    score: 1,
    kind_id: null,
    measurement_unit_id: 0,
    measurement_unit_name: '',
    uploadFiles: [],
  })

  // 重置编辑步骤
  editStep.value = 1
}
const { fetchData: EditfetchData, success: editSuccess, msg: editMsg } = updateFpmQualityCheck()
async function EditHandleSure(formData: any) {
  await EditfetchData(formData)
  if (editSuccess.value)
    ElMessage.success('提交成功')
  else
    ElMessage.error(editMsg.value)

  getData()
}

watch(
  () => form.stock_id,
  () => {
    if (form?.stock_id) {
      state.pid = 0
      tableOptions.datalist = []
      finishProductionOptions.tableConfig.rowIndex = ''
    }
  },
  { deep: true },
)

// 监听疵点列表变化，确保分类计数正确更新
watch(
  () => state.ciDianList,
  () => {
    // 疵点列表变化时，分类计数会自动更新（通过computed属性）
    console.log('疵点列表已更新，分类计数将自动刷新')
  },
  { deep: true },
)

// const changeDate = () => {
//   state.pid = 0
// }

const store = useGlobalData()
function clearFormData() {
  if (isConnected.value)
    measureMeterStore.measureMeterState.handleZero()
  nextTick(() => {
    form.product_code = ''
    form.product_name = ''
    form.product_color_code = ''
    form.product_color_name = ''
    form.dyelot_number = ''
    form.stock_id = ''
    form.user_name = ''
    form.user_id = ''
    form.scanCode = ''
    form.volume_number = ''
    form.finish_product_width_and_unit_name = ''
    form.finish_product_gram_weight_and_unit_name = ''
  })
  // 清空扫码记录，不然监听不到下一个一样的
  store.clearScanerCode()
  ElMessage.success('清空完成')
}

const { fetchData: getFpmQualityCheckByStockId, data: fpmQualityCheckData } = GetFpmQualityCheckByStockId()
const { fetchData: addOrUpdateFpmQualityCheck, success: addOrUpdateFpmQualityCheckSuccess, msg: addOrUpdateFpmQualityCheckMsg } = AddOrUpdateFpmQualityCheck()
async function getFpmQualityCheckByStockIdFn(stock_id: number) {
  if (!stock_id)
    return
  await getFpmQualityCheckByStockId({ stock_id: form.stock_id })
  fpmQualityCheckData.value = processDataOut(fpmQualityCheckData.value)

  // 直接展示疵点信息
  if (!fpmQualityCheckData.value?.id)
    return

  await getCiDianTable(fpmQualityCheckData.value?.id)
}

// 布匹基础信息录入-提交
async function fomQualityCheckDataSubmit() {
  if (!activeStockId)
    return ElMessage.error('还未扫码或者从库存中添加数据')
  const data = processDataIn(state) as other.TfpmQualityCheckData
  await addOrUpdateFpmQualityCheck({
    ...data,
    stock_id: activeStockId || 0,
  })
  if (!addOrUpdateFpmQualityCheckSuccess.value)
    return ElMessage.error(addOrUpdateFpmQualityCheckMsg.value)
  ElMessage.success('提交成功')
  await getData()
}
const activeName = ref('all')

// 疵点分类相关
const defectCategories = ref<any[]>([])
// 疵点分类API
const { fetchData: fetchDefectCategories, data: defectCategoriesData, success: defectCategoriesSuccess } = GetDictionaryDetailEnumListApi()

// 加载疵点分类
async function loadDefectCategories() {
  try {
    await fetchDefectCategories({ dictionary_id: DictionaryType.defectKind })
    if (defectCategoriesSuccess.value) {
      const categories = defectCategoriesData.value?.list || []
      defectCategories.value = [
        { id: 'all', name: '全部疵点' },
        ...categories.map((item: any) => ({
          id: item.id,
          name: item.name,
        })),
      ]
      console.log('疵点分类加载成功:', defectCategories.value.length, '个分类')
    }
    else {
      console.warn('疵点分类API调用成功但返回失败状态')
      setDefaultCategories()
    }
  }
  catch (error) {
    console.error('加载疵点分类失败:', error)
    setDefaultCategories()
  }
}

// 设置默认分类
function setDefaultCategories() {
  defectCategories.value = [
    { id: 'all', name: '全部疵点' },
  ]
  console.log('使用默认疵点分类')
}

// 根据当前选中的分类过滤疵点
const filteredDefectList = computed(() => {
  if (activeName.value === 'all')
    return state.ciDianList

  // 根据分类ID过滤疵点
  const categoryId = Number(activeName.value)
  return state.ciDianList.filter((item: any) => item.kind_id === categoryId)
})

// 计算每个分类下的疵点数量
const defectCategoriesWithCount = computed(() => {
  return defectCategories.value.map((category) => {
    if (category.id === 'all') {
      return {
        ...category,
        count: state.ciDianList.length,
        displayName: `${category.name}(${state.ciDianList.length})`,
      }
    }

    const categoryId = Number(category.id)
    const count = state.ciDianList.filter((item: any) => item.kind_id === categoryId).length
    return {
      ...category,
      count,
      displayName: `${category.name}(${count})`,
    }
  })
})

function handleClickTabs(tab: any, event: Event) {
  console.log('切换疵点分类:', tab.props.name, tab.props.label)
  // 这里可以添加切换分类时的额外逻辑，比如统计等

  // 可以在这里添加分类切换的统计或其他业务逻辑
  const currentCategory = defectCategoriesWithCount.value.find(cat => cat.id === tab.props.name)
  if (currentCategory)
    console.log(`当前分类: ${currentCategory.name}, 疵点数量: ${currentCategory.count}`)
}

// 数字键盘相关方法
function inputNumber(num: number) {
  // 根据当前步骤处理不同的输入
  switch (editStep.value) {
    case 1: // 疵点位置
      if (defectForm.defect_position === '0')
        defectForm.defect_position = String(num)
      else
        defectForm.defect_position += String(num)
      break
    case 2: // 疵点个数
      if (defectForm.defect_count === '0')
        defectForm.defect_count = String(num)
      else
        defectForm.defect_count += String(num)
      break
    case 3: // 疵点打分 - 只允许1234
      if ([1, 2, 3, 4].includes(num))
        defectForm.score = num

      break
  }
}

function inputDot() {
  // 只有在疵点位置步骤才允许输入小数点
  if (editStep.value === 1 && !defectForm.defect_position.includes('.'))
    defectForm.defect_position += '.'
}

function deleteNumber() {
  switch (editStep.value) {
    case 1: // 疵点位置
      if (defectForm.defect_position.length > 1)
        defectForm.defect_position = defectForm.defect_position.slice(0, -1)
      else
        defectForm.defect_position = '0'
      break
    case 2: // 疵点个数
      if (defectForm.defect_count.length > 1)
        defectForm.defect_count = defectForm.defect_count.slice(0, -1)
      else
        defectForm.defect_count = '0'
      break
    case 3: // 疵点打分 - 重置为1
      defectForm.score = 1
      break
  }
}

function nextDefect() {
  if (editStep.value === 3) {
    // 第三步点击确定，保存疵点
    saveNewDefect()
    // 重置到第一步
    editStep.value = 1
    // 滚动到位置输入区域
    scrollToStep(1)
  }
  else {
    // 进入下一步
    editStep.value++
    ElMessage.info(`进入${stepTitle.value}编辑`)
    // 滚动到对应步骤
    scrollToStep(editStep.value)
  }
}

// 滚动到指定步骤的函数
function scrollToStep(step: number) {
  nextTick(() => {
    let targetRef
    switch (step) {
      case 1:
        targetRef = positionStepRef.value
        break
      case 2:
        targetRef = countStepRef.value
        break
      case 3:
        targetRef = scoreStepRef.value
        break
      default:
        return
    }

    if (targetRef && scrollbarRef.value) {
      // 使用Element Plus scrollbar的scrollTo方法
      const scrollContainer = scrollbarRef.value.wrapRef
      if (scrollContainer) {
        const targetElement = targetRef
        const containerRect = scrollContainer.getBoundingClientRect()
        const targetRect = targetElement.getBoundingClientRect()

        // 计算目标元素相对于滚动容器的位置
        const scrollTop = scrollContainer.scrollTop
        const targetOffsetTop = targetRect.top - containerRect.top + scrollTop

        // 滚动到目标位置，留一些边距
        const offset = 20
        scrollContainer.scrollTo({
          top: Math.max(0, targetOffsetTop - offset),
          behavior: 'smooth',
        })
      }
    }
  })
}

// 切换到指定步骤并滚动定位
function switchToStep(step: number) {
  editStep.value = step
  scrollToStep(step)
}

// 上传文件相关方法
function handleUploadSuccess(fileList: string[]) {
  defectForm.uploadFiles = fileList
  console.log('上传成功的文件列表:', fileList)
}

function handleUploadChange(fileList: string[]) {
  defectForm.uploadFiles = fileList
}

// 拖拽分割器相关方法
function handleMouseDown(event: MouseEvent) {
  startDrag(event.clientX)
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

function handleMouseMove(event: MouseEvent) {
  if (!isDragging.value)
    return
  updateDrag(event.clientX)
}

function handleMouseUp() {
  endDrag()
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
}

// 触摸事件处理
function handleTouchStart(event: TouchEvent) {
  event.preventDefault() // 防止页面滚动
  const touch = event.touches[0]
  startDrag(touch.clientX)
  document.addEventListener('touchmove', handleTouchMove, { passive: false })
  document.addEventListener('touchend', handleTouchEnd)
}

function handleTouchMove(event: TouchEvent) {
  if (!isDragging.value)
    return
  event.preventDefault() // 防止页面滚动
  const touch = event.touches[0]
  updateDrag(touch.clientX)
}

function handleTouchEnd() {
  endDrag()
  document.removeEventListener('touchmove', handleTouchMove)
  document.removeEventListener('touchend', handleTouchEnd)
}

// 通用拖拽逻辑
function startDrag(clientX: number) {
  isDragging.value = true
  startX.value = clientX
  startLeftWidth.value = leftPanelWidth.value
  document.body.style.cursor = 'col-resize'
  document.body.style.userSelect = 'none'
}

function updateDrag(clientX: number) {
  const containerWidth = document.querySelector('.resizable-container')?.clientWidth || 1000
  const deltaX = clientX - startX.value
  const deltaPercent = (deltaX / containerWidth) * 100

  let newLeftWidth = startLeftWidth.value + deltaPercent

  // 限制最小和最大宽度
  newLeftWidth = Math.max(20, Math.min(80, newLeftWidth))

  leftPanelWidth.value = newLeftWidth
}

function endDrag() {
  isDragging.value = false
  document.body.style.cursor = ''
  document.body.style.userSelect = ''
}

// 清理事件监听器
function cleanupDragListeners() {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
  document.removeEventListener('touchmove', handleTouchMove)
  document.removeEventListener('touchend', handleTouchEnd)
}

// 组件卸载时清理
onUnmounted(() => {
  cleanupDragListeners()
})
function handleClickScore(score: number) {
  if (editStep.value === 3) {
    defectForm.score = score
  }
  else {
    editStep.value = 3
    defectForm.score = score
    // 滚动到分数选择区域
    scrollToStep(3)
  }
}
const showRecord = ref(false)
function handleDefectRecord() {
  showRecord.value = true
}
const showSameDyelot = ref(false)
function handleSameDyelot() {
  showSameDyelot.value = true
}
function handleClickAddNew() {
  console.log('handleClickAddNew')
  AddNewDefectRef.value.state.showModal = true
  AddNewDefectRef.value.state.modalName = '验布疵点定义'
  AddNewDefectRef.value.state.form.code = ''
  AddNewDefectRef.value.state.form.measurement_unit_id = 0
  AddNewDefectRef.value.state.form.sort = 0
  AddNewDefectRef.value.state.form.name = ''
  AddNewDefectRef.value.state.form.remark = ''
  AddNewDefectRef.value.state.form.id = -1
}
const { fetchData: AddFetch, msg: AddMsg, success: AddSuccess } = addInfoBasicDefect()

// 新建
async function newDefectHandleSure(form: any) {
  const query = {
    ...form,
    sort: Number(form.sort),
  }
  await AddFetch(query)
  if (AddSuccess.value) {
    ElMessage.success('新增疵点成功')
    AddNewDefectRef.value.state.showModal = false
    init()
  }
  else {
    ElMessage.error(AddMsg.value)
  }
}
function unitSelectChange(val: any) {
  defectForm.measurement_unit_name = val.name
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="" class="">
      <el-form label-position="left" :inline="true" size="large" label-width="100px">
        <el-form-item label="布匹条码:">
          <div class="flex flex-row">
            <ScanerInput v-model="form.scanCode" @handle-sure="finishProductionOptions.handleSure" />
            <el-button size="large" @click="clearFormData">
              清空
            </el-button>
            <el-button type="primary" size="large" @click="showDialog">
              从库存中查询
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="码表:">
          <el-space>
            <el-button size="large" @click="handleConnectToSerialPort">
              连接码表({{ isConnected ? '已连接✅' : '未连接❌' }})
            </el-button>
            <el-button size="large" @click="handleZero">
              清零
            </el-button>
            <el-button size="large" @click="handleDisconnectToSerialPort">
              断开码表
            </el-button>
            <el-button size="large" @click="toggleLog">
              日志
            </el-button>
            <Log v-if="showLog" />
          </el-space>
        </el-form-item>
      </el-form>
      <div class="flex items-stretch gap-2">
        <el-form label-position="left" size="large" label-width="100px" class="form-container flex-1">
          <div class="form-row">
            <el-form-item label="成品名称:">
              <span>{{ formatHashTag(form?.product_code || '-', form?.product_name || '-') }}</span>
            </el-form-item>
            <el-form-item label="成品工艺:">
              <span>{{ form?.finish_product_craft || '-' }}</span>
            </el-form-item>
            <el-form-item label="包边门幅:">
              <el-input v-model="state.edge_width" />
            </el-form-item>
            <el-form-item label="次布数量:">
              <el-input v-model="state.defect_weight" />
            </el-form-item>
            <el-form-item label="克重:">
              <el-input v-model="state.qc_gram_weight" />
            </el-form-item>
          </div>

          <div class="form-row">
            <el-form-item label="颜色色号:">
              <span>{{ formatHashTag(form?.product_color_code || '-', form?.product_color_name || '-') }}</span>
            </el-form-item>
            <el-form-item label="幅宽克重:">
              <span>{{ form?.finish_product_width_and_unit_name || '-' }} {{ form?.finish_product_gram_weight_and_unit_name || '-' }}</span>
            </el-form-item>
            <el-form-item label="有效门幅:">
              <el-input v-model="state.useful_width" type="number" />
            </el-form-item>
            <el-form-item label="实际数量:">
              <el-input v-model="state.actually_weight" type="number" />
            </el-form-item>
            <el-form-item label="备注:">
              <el-input v-model="state.remark" />
            </el-form-item>
          </div>
        </el-form>
        <el-button type="primary" plain class="w-[100px] self-stretch confirm-button" @click="fomQualityCheckDataSubmit">
          确认修改
        </el-button>
      </div>
    </FildCard>

    <div class="flex w-full h-full resizable-container">
      <!-- 左侧面板 -->
      <FildCard
        no-shadow
        class="flex flex-col h-full"
        :tool-bar="false"
        :style="{ width: `${leftPanelWidth}%` }"
      >
        <el-tabs v-model="activeName" addable class="flex-1 defect-tabs overflow-hidden" @tab-click="handleClickTabs">
          <template #add-icon>
            <el-link :underline="false" @click="handleClickAddNew">
              <el-icon><Plus /></el-icon>
              添加疵点
            </el-link>
          </template>

          <!-- 动态疵点分类 -->
          <el-tab-pane
            v-for="category in defectCategoriesWithCount"
            :key="category.id"
            :label="category.displayName"
            :name="category.id"
          >
            <!-- 骨架屏加载状态 -->
            <el-skeleton v-if="defectListLoading" :loading="defectListLoading" animated>
              <template #template>
                <div class="flex flex-wrap gap-2">
                  <el-skeleton-item
                    v-for="i in 12"
                    :key="i"
                    variant="button"
                    style="width: 80px; height: 32px; border-radius: 16px;"
                  />
                </div>
              </template>
            </el-skeleton>

            <!-- 实际疵点按钮列表 -->
            <template v-else>
              <div class="overflow-y-auto tabs-container">
                <el-button v-for="item in filteredDefectList" :key="item.id" round size="large" @click="handleClick(item)">
                  {{ item.name }}
                </el-button>
                <el-button round size="large" @click="handleOtherClick()">
                  其他
                </el-button>
              </div>
            </template>
          </el-tab-pane>
        </el-tabs>
        <div class="flex flex-wrap mt-1.5 gap-1">
          <el-button size="large" @click="handleDefectRecord">
            疵点记录({{ tableOptions.datalist.length }})
          </el-button>
          <el-button size="large" @click="handleSameDyelot">
            同缸(已验{{ finishProductionOptions.datalist.length }}/{{ form.total_roll || 0 }})
          </el-button>
        </div>
      </FildCard>

      <!-- 可拖拽的分割器 -->
      <div
        class="resizer"
        :class="{ resizing: isDragging }"
        title="拖拽调整面板大小"
        @mousedown="handleMouseDown"
        @touchstart="handleTouchStart"
      >
        <div class="resizer-line" />
      </div>

      <!-- 右侧面板 -->
      <FildCard
        no-shadow
        class="min-w-unset right-panel"
        :tool-bar="false"
        :style="{ width: `${100 - leftPanelWidth}%` }"
      >
        <div class="flex">
          <!-- 左侧疵点信息 -->
          <div class="flex-1 p-4 border-r border-gray-200 flex flex-col">
            <!-- 可滚动的内容区域 -->
            <el-scrollbar ref="scrollbarRef" class="flex-1">
              <div class="flex flex-col gap-2 pr-2">
                <!-- 基础信息 -->
                <div class="space-y-2">
                  <div class="flex items-center">
                    <span class="font-medium">缸号:</span>
                    <span class="ml-2 text-blue-600 ">{{ form?.dyelot_number || '' }}</span>
                  </div>
                  <div class="flex items-center">
                    <span class="text-gray-600">卷号:</span>
                    <span class="ml-2 text-blue-600 ">{{ form?.volume_number || '' }}</span>
                  </div>
                </div>

                <!-- 疵点表单 -->
                <el-form
                  ref="defectFormRef"
                  label-position="left"
                  :model="defectForm"
                  :rules="defectFormRules"
                  label-width="100px"
                  require-asterisk-position="right"
                  size="large"
                  class="defect-form-grid"
                >
                  <!-- 疵点编号 -->
                  <el-form-item v-if="isOther" class="space-y-2" label="疵点编号" prop="defect_code">
                    <el-input
                      v-model="defectForm.defect_code"
                      placeholder="疵点编号(非必填)"
                      size="default"
                      style="width: 200px"
                      clearable
                    />
                  </el-form-item>

                  <!-- 疵点名称 -->
                  <el-form-item class="space-y-2" label="疵点名称" prop="defect_name">
                    <span v-if="!isOther" class="text-gray-800">{{ defectForm.defect_name || '' }}</span>
                    <el-input
                      v-else
                      v-model="defectForm.defect_name"
                      placeholder="疵点名称"
                      size="default"
                      style="width: 200px"
                      clearable
                    />
                  </el-form-item>

                  <!-- 单位名称 -->
                  <el-form-item class="space-y-2" label="单位名称" prop="measurement_unit_id">
                    <SelectComponents
                      v-if="isOther"
                      v-model="defectForm.measurement_unit_id"
                      size="default"
                      style="width: 200px"
                      api="getInfoBaseMeasurementUnitList"
                      label-field="name"
                      value-field="id"
                      clearable
                      @change-value="unitSelectChange"
                    />
                    <span v-else>
                      {{ defectForm.measurement_unit_name }}
                    </span>
                  </el-form-item>

                  <!-- 疵点类别 -->
                  <el-form-item v-if="isOther" class="space-y-2" label="疵点类别" prop="kind_id">
                    <SelectComponents
                      v-model="defectForm.kind_id"
                      style="width: 200px"
                      api="GetDictionaryDetailEnumListApi"
                      placeholder="请选择疵点种类(非必填)"
                      label-field="name"
                      value-field="id"
                      :query="{ dictionary_id: DictionaryType.defectKind }"
                      clearable
                    />
                  </el-form-item>

                  <!-- 位置输入 -->
                  <div ref="positionStepRef" :class="{ 'ring-2 ring-blue-500 ring-opacity-50 rounded p-2': editStep === 1 }" @click="switchToStep(1)">
                    <el-form-item class="space-y-2 form-item-full-width" label="疵点位置" prop="defect_position" :rules="defectFormRules.defect_position">
                      <el-input
                        v-model="defectForm.defect_position"
                        class="flex-1"
                        size="large"
                        readonly
                        :value="defectForm.defect_position || ''"
                      >
                        <template #prefix>
                          <span class="text-gray-500">第</span>
                        </template>
                        <template #suffix>
                          <span class="text-gray-500">米</span>
                        </template>
                      </el-input>
                    </el-form-item>
                  </div>

                  <!-- 个数输入 -->
                  <div ref="countStepRef" class="space-y-2" :class="{ 'ring-2 ring-blue-500 ring-opacity-50 rounded p-2': editStep === 2 }" @click="switchToStep(2)">
                    <el-form-item class="form-item-full-width" label="疵点数量" prop="defect_count" :rules="defectFormRules.defect_count">
                      <el-input
                        v-model="defectForm.defect_count"
                        size="large"
                        readonly
                        :value="defectForm.defect_count || '0'"
                      />
                    </el-form-item>
                  </div>

                  <!-- 分数选择 -->
                  <div ref="scoreStepRef" class="space-y-2 form-item-full-width" :class="{ 'ring-2 ring-blue-500 ring-opacity-50 rounded p-2': editStep === 3 }" @click="switchToStep(3)">
                    <span :class="editStep === 3 ? 'text-blue-600 font-medium' : 'text-gray-600'">分数:</span>
                    <div class="flex score-buttons">
                      <el-button
                        v-for="score in [1, 2, 3, 4]"
                        :key="score"
                        :type="defectForm.score === score ? 'primary' : 'default'"
                        size="large"
                        class="w-12 h-10"
                        @click="handleClickScore(score)"
                      >
                        {{ score }}
                      </el-button>
                    </div>
                  </div>

                  <!-- 上传区域 -->
                  <el-form-item class="form-item-full-width" label-position="top" label="上传图片" prop="uploadFiles">
                    <UploadFile
                      v-model:file-list="defectForm.uploadFiles"
                      :dragable="true"
                      :multiple="true"
                      accept="image/*,.pdf,.doc,.docx"
                      secene="defect"
                      :auto-upload="true"
                      :image-shown="true"
                      additional-text="支持图片、PDF、Word文档，文件大小不超过10M(非必填)"
                      @on-upload-success="handleUploadSuccess"
                      @update:file-list="handleUploadChange"
                    />
                  </el-form-item>
                </el-form>
              </div>
            </el-scrollbar>

            <!-- 固定在底部的操作区域 -->
            <div class="pt-4 border-t border-gray-200 space-y-3">
              <!-- 保存按钮 -->
              <el-button type="primary" size="large" class="w-full" @click="saveNewDefect">
                保存疵点
              </el-button>
              <el-checkbox v-if="isOther" v-model="isSyncToDefectData" type="primary" size="large">
                是否同步到疵点资料
              </el-checkbox>
            </div>
          </div>

          <!-- 右侧数字键盘 -->
          <div class="digital-keyboard keyboard-container">
            <div class="space-y-4">
              <div class="text-center">
                <h3 class="text-lg font-medium text-gray-800">
                  {{ stepTitle }}
                </h3>
                <p class="text-sm text-gray-500 mt-1">
                  {{ editStep === 1 ? '请输入位置' : editStep === 2 ? '请输入个数' : '请选择分数' }}
                </p>
              </div>

              <!-- 数字键盘 -->
              <div class="grid grid-cols-3 gap-2">
                <button
                  v-for="num in [1, 2, 3, 4, 5, 6, 7, 8, 9]"
                  :key="num"
                  :disabled="editStep === 3 && ![1, 2, 3, 4].includes(num)"
                  class="h-12 rounded text-lg font-medium transition-colors" :class="[
                    editStep === 3 && ![1, 2, 3, 4].includes(num)
                      ? 'bg-gray-100 border border-gray-200 text-gray-400 cursor-not-allowed'
                      : 'bg-white border border-gray-300 hover:bg-gray-50 active:bg-gray-100',
                    editStep === 3 && defectForm.score === num ? '!bg-[#409eff] !border-blue-500 !text-white' : '',
                  ]"
                  @click="inputNumber(num)"
                >
                  {{ num }}
                </button>
                <button
                  :disabled="editStep === 3"
                  class="h-12 rounded text-lg font-medium transition-colors" :class="[
                    editStep === 3
                      ? 'bg-gray-100 border border-gray-200 text-gray-400 cursor-not-allowed'
                      : 'bg-white border border-gray-300 hover:bg-gray-50 active:bg-gray-100',
                  ]"
                  @click="inputNumber(0)"
                >
                  0
                </button>
                <button
                  :disabled="editStep !== 1"
                  class="h-12 rounded text-lg font-medium transition-colors" :class="[
                    editStep !== 1
                      ? 'bg-gray-100 border border-gray-200 text-gray-400 cursor-not-allowed'
                      : 'bg-white border border-gray-300 hover:bg-gray-50 active:bg-gray-100',
                  ]"
                  @click="inputDot"
                >
                  •
                </button>
                <button
                  class="h-12 bg-white border border-gray-300 text-white rounded text-lg font-medium hover:bg-gray-50 active:bg-gray-100 flex items-center justify-center transition-colors"
                  @click="deleteNumber"
                >
                  <svg-icon name="delete-left" size="2rem" />
                  <!-- <el-icon><Delete /></el-icon> -->
                </button>
              </div>

              <!-- 下一个/确定按钮 -->
              <el-button type="primary" size="large" class="w-full mt-4" @click="nextDefect">
                {{ stepButtonText }}
              </el-button>
            </div>
          </div>
        </div>
      </FildCard>
    </div>
  </div>
  <vxe-modal v-model="showRecord" title="疵点记录" width="1000" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="flex flex-col h-full">
      <div class="file_card flex-auto">
        <Table class="h-full" :config="tableOptions.tableConfig" :table-list="tableOptions.datalist" :column-list="tableOptions.columnList">
          <template #operate="{ row }">
            <el-link @click="tableOptions.edit(row)">
              修改
            </el-link>
            <el-link style="margin-left: 10px" @click="tableOptions.delete(row)">
              删除
            </el-link>
          </template>
        </Table>
      </div>
    </div>
  </vxe-modal>
  <vxe-modal v-model="showSameDyelot" title="同缸" width="1000" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="file_card">
      <Table ref="tablesRef1" :config="finishProductionOptions.tableConfig" :table-list="finishProductionOptions.datalist" :column-list="finishProductionOptions.columnList">
        <template #operate="{ row }">
          <el-button v-has="'FpQualityCheckEdit'" type="primary" text link @click="finishProductionOptions.edit(row)">
            编辑
          </el-button>
          <PrintPopoverBtn
            :id="row.id"
            :key="row.id"
            v-has="'FpQualityCheckExport'"
            print-btn-type="text"
            style="width: auto"
            api="GetQualityCheckDetail"
            :options="printOptions"
          />
        </template>
      </Table>
    </div>
  </vxe-modal>
  <StockDialog ref="StockDialogRef" @handle-sure="finishProductionOptions.handleSure" />
  <AddDialog ref="AddDialogRef" @handle-sure="defectHandleSure" @on-hide="handleAddDialogHide" />
  <AddNewDefectDialog ref="AddNewDefectRef" @handle-sure="newDefectHandleSure" />
  <EditDialog ref="EditDialogRef" @handle-sure="EditHandleSure" />
</template>

<style lang="scss" scoped>
.items {
  display: flex;
  flex-wrap: wrap;

  .item {
    padding: 6px 14px;
    text-align: center;
    background: #fff;
    color: #0e7eff;
    border: 1px solid #0e7eff;
    cursor: pointer;
    user-select: none;
    font-size: 14px;
    min-width: 100px;

    &.active {
      background: #0e7eff;
      color: #fff;
    }
  }
}

/* 疵点按钮滚动区域样式 */
.defect-buttons-scrollbar {
  max-height: 200px; /* 设置最大高度，超出时显示滚动条 */
  overflow: hidden;
  margin-bottom: 10px;
}

/* 表单两列布局 */
.defect-form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 10px;
}

/* 占据整行的表单项 */
.form-item-full-width {
  grid-column: 1 / -1;
}

/* 在中等宽度时启用两列布局 */
@media screen and (min-width: 1280px) {
  .defect-form-grid {
    grid-template-columns: 1fr 1fr;
    gap: 12px 20px;
  }

  /* 调整标签宽度以适应两列布局 */
  .defect-form-grid .el-form-item__label {
    width: 90px !important;
    font-size: 13px;
  }
}

/* 在更大宽度时优化间距 */
@media screen and (min-width: 1440px) {
  .defect-form-grid {
    gap: 16px 24px;
  }

  .defect-form-grid .el-form-item__label {
    width: 100px !important;
    font-size: 14px;
  }
}

/* 优化滚动条样式 */
.defect-buttons-scrollbar :deep(.el-scrollbar__bar.is-vertical) {
  width: 6px;
}

.defect-buttons-scrollbar :deep(.el-scrollbar__bar.is-vertical .el-scrollbar__thumb) {
  background-color: #c1c1c1;
  border-radius: 3px;
}

.defect-buttons-scrollbar :deep(.el-scrollbar__bar.is-vertical .el-scrollbar__thumb:hover) {
  background-color: #a8a8a8;
}

:deep(.tabs-container){
  padding: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;

  .el-button{
    margin-right: 0;
  }

  .el-button+.el-button {
    margin-right: 0;
    margin-left: 0;
  }
}
:deep(.el-tabs__new-tab){
  border: none;
  width: auto;
}
.form-container {
  .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 10px;
    &:last-of-type{
      margin-bottom: 0;
    }
    .el-form-item {
      flex: 1;
      margin-bottom: 0;

      &.full-width {
        flex: 3;
      }

      span {
        color: #606266;
        font-size: 14px;
      }
    }
  }
}

.confirm-button {
  height: auto !important;
  min-height: auto !important;
  align-self: stretch !important;
}

/* 可拖拽分割器样式 */
.resizable-container {
  position: relative;
}

.resizer {
  width: 12px;
  background: #f5f5f5;
  border-left: 1px solid #d0d0d0;
  border-right: 1px solid #d0d0d0;
  cursor: col-resize;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
  /* 触摸优化 */
  touch-action: none;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.resizer:hover {
  background: #e0e0e0;
}

.resizer.resizing {
  background: #409eff;
}

.resizer-line {
  width: 3px;
  height: 60px;
  background: #999;
  border-radius: 2px;
}

.resizer:hover .resizer-line {
  background: #909090;
}

.resizer.resizing .resizer-line {
  background: white;
}

.resizer-dots {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.dot {
  width: 4px;
  height: 4px;
  background: #666;
  border-radius: 50%;
}

.resizer:hover .dot {
  background: #333;
}

.resizer.resizing .dot {
  background: white;
}

/* 移动端触摸优化 */
@media (hover: none) and (pointer: coarse) {
  .resizer {
    width: 16px; /* 在触摸设备上增加宽度，便于手指操作 */
  }

  .resizer-line {
    width: 4px !important; /* 在触摸设备上增加线条宽度 */
    height: 80px !important; /* 增加高度，便于触摸 */
  }
}

/* 防止触摸时的默认行为 */
.resizer * {
  pointer-events: none;
}

/* ===== 响应式设计适配 ===== */

/* 1280x1024 分辨率适配 (4:3 比例) */
@media screen and (min-width: 1280px) and (max-width: 1366px) and (min-height: 1024px) {
  .list-page {
    padding: 8px;
    font-size: 14px;
  }

  .resizable-container {
    height: calc(100vh - 180px);
    overflow: hidden;
  }

  /* 数字键盘区域 */
  .digital-keyboard {
    width: 200px !important;
    padding: 12px !important;
  }

  .digital-keyboard h3 {
    font-size: 14px !important;
  }

  .digital-keyboard button {
    height: 36px !important;
    font-size: 13px !important;
  }

  /* 疵点按钮优化 */
  .el-button[round] {
    padding: 6px 16px;
    margin: 2px;
    font-size: 12px;
  }

  /* 右侧面板内容区域 */
  .flex-1.p-4 {
    padding: 12px !important;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
  }
}

/* 专门针对1280x1024分辨率的精确优化 */
@media screen and (width: 1280px) and (height: 1024px) {
  .digital-keyboard {
    width: 180px !important;
    padding: 10px !important;
  }

  .digital-keyboard h3 {
    font-size: 13px !important;
    margin-bottom: 8px;
  }

  .digital-keyboard button {
    height: 32px !important;
    font-size: 12px !important;
    margin: 1px;
  }

  /* 调整左侧面板宽度，给更多空间 */
  .right-panel .flex-1.p-4.border-r {
    padding: 10px !important;
  }
}

/* 中等分辨率优化 (1280-1440px 宽度范围) */
@media screen and (min-width: 1280px) and (max-width: 1440px) {
  .digital-keyboard {
    width: 190px !important;
  }

  /* 确保左右面板比例更协调 */
  .right-panel .flex {
    gap: 8px;
  }
}

/* 1920x1080 分辨率适配 (16:9 比例) */
@media screen and (min-width: 1920px) and (min-height: 1080px) {
  .list-page {
    padding: 16px;
    font-size: 16px;
  }

  .resizable-container {
    // height: calc(100vh - 200px);
    overflow: hidden;
  }

  /* 左侧面板优化 */
  .el-tabs__content {
    padding: 20px;
    // max-height: calc(100vh - 300px);
    overflow-y: auto;
  }

  .el-button {
    margin: 4px;
    padding: 12px 20px;
    font-size: 15px;
  }

  /* 右侧面板优化 */
  .el-card.min-w-unset {
    min-height: calc(100vh - 200px);
    overflow-y: auto;
  }

  /* 右侧表单区域 */
  .el-form-item {
    margin-bottom: 18px;
  }

  .el-form-item__label {
    font-size: 15px;
    font-weight: 500;
  }

  .el-input {
    font-size: 15px;
  }

  /* 数字键盘区域 */
  .digital-keyboard {
    width: 280px !important;
  }

  /* 疵点按钮优化 */
  .el-button[round] {
    padding: 10px 20px;
    margin: 4px;
    font-size: 14px;
    min-width: 80px;
  }

  /* 分割器在大屏幕上的优化 */
  .resizer {
    width: 14px;
  }

  .resizer-line {
    width: 4px;
    height: 80px;
  }

  /* 右侧面板内容区域 */
  .flex-1.p-4 {
    max-height: calc(100vh - 220px);
    overflow-y: auto;
  }
}

/* 通用响应式布局 */
.resizable-container {
  display: flex;
  overflow: hidden;
  flex: 1;
}

/* 右侧面板通用样式 */
.flex-1.p-4.border-r {
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* 数字键盘容器 */
.digital-keyboard {
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* 确保上传组件不会过大 */
.el-upload {
  max-height: 150px;
  overflow-y: auto;
}

/* 右侧面板专用样式 */
.right-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.right-panel .flex {
  flex: 1;
  overflow: hidden;
}

/* 左侧疵点信息区域样式 */
.right-panel .flex-1.p-4.border-r {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 滚动区域样式 */
.right-panel .el-scrollbar {
  flex: 1;
  overflow: hidden;
}

.right-panel .digital-keyboard {
  flex-shrink: 0;
  overflow-y: auto;
  max-height: 100%;
  width: 256px; /* 默认宽度 w-64 */
  padding: 16px;
}

/* 滚动条优化 */
.right-panel .el-scrollbar :deep(.el-scrollbar__bar.is-vertical .el-scrollbar__thumb) {
  background-color: #c1c1c1;
  border-radius: 3px;
}

.right-panel .el-scrollbar :deep(.el-scrollbar__bar.is-vertical .el-scrollbar__thumb:hover) {
  background-color: #a8a8a8;
}

.digital-keyboard::-webkit-scrollbar {
  width: 6px;
}

.digital-keyboard::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.digital-keyboard::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.digital-keyboard::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 特殊屏幕比例适配 */
@media screen and (max-height: 900px) {
  .resizable-container {
    height: calc(100vh - 160px);
  }

  .right-panel .flex-1.p-4.border-r {
    padding: 8px;
  }

  .right-panel .flex-1.p-4.border-r .pr-2 {
    padding-right: 4px;
  }

  .digital-keyboard {
    padding: 8px !important;
  }

  :deep(.el-form-item) {
    margin-bottom: 8px;
  }

  /* 小屏幕下减小疵点按钮区域高度 */
  .defect-buttons-scrollbar {
    max-height: 150px;
  }

  /* 小屏幕强制使用单列布局 */
  .defect-form-grid {
    grid-template-columns: 1fr !important;
    gap: 8px !important;
  }
}

@media screen and (max-height: 768px) {
  .resizable-container {
    height: calc(100vh - 140px);
  }

  .digital-keyboard h3 {
    font-size: 14px !important;
    margin-bottom: 8px;
  }

  .digital-keyboard button {
    height: 32px !important;
    font-size: 12px !important;
  }

  :deep(.el-form-item) {
    margin-bottom: 6px;
  }

  /* 更小屏幕下进一步减小疵点按钮区域高度 */
  .defect-buttons-scrollbar {
    max-height: 120px;
  }

  /* 更小屏幕强制使用单列布局 */
  .defect-form-grid {
    grid-template-columns: 1fr !important;
    gap: 6px !important;
  }

  :deep(.el-form-item__label) {
    font-size: 12px !important;
  }
}

/* 宽屏适配 */
@media screen and (min-aspect-ratio: 16/9) {
  .digital-keyboard {
    width: 300px !important;
  }
}

/* 窄屏适配 */
@media screen and (max-aspect-ratio: 4/3) {
  .digital-keyboard {
    width: 160px !important;
  }

  .digital-keyboard button {
    height: 28px !important;
    font-size: 11px !important;
  }
}

/* 美化右侧面板 */
.right-panel {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 0 12px 12px 0;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.05);
}

/* 美化左侧面板 */
.flex-col.h-full {
  background: linear-gradient(135deg, #ffffff 0%, #f5f7fa 100%);
  border-radius: 12px 0 0 12px;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.05);
}

/* 美化分割器 */
.resizer {
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  border-radius: 0;
  z-index: 10;
}

/* 美化表单区域 */
.flex-1.p-4.border-r {
  border-right: none !important;
  border-right: 1px solid rgba(0, 0, 0, 0.05) !important;
}

/* 美化数字键盘 */
.keyboard-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  margin: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.defect-tabs :deep(.el-tabs__content) {
  overflow-y: auto;
}

/* 疵点按钮容器 */
.defect-tabs .el-tabs__content .flex-wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: flex-start;
}

/* 适配不同分辨率的标签页 */
@media screen and (max-width: 1366px) {
  .defect-tabs .el-tabs__item {
    padding: 0 16px;
    height: 36px;
    line-height: 36px;
    font-size: 13px;
  }
}

@media screen and (min-width: 1920px) {
  .defect-tabs .el-tabs__item {
    padding: 0 24px;
    height: 48px;
    line-height: 48px;
    font-size: 16px;
  }

  .defect-tabs .el-tabs__content {
    padding: 24px;
  }
}

/* 分割器美化 */
.resizer {
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
  border: none;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.resizer:hover {
  background: linear-gradient(180deg, #e9ecef 0%, #dee2e6 100%);
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
}

.resizer.resizing {
  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
}

.resizer-line {
  background: linear-gradient(180deg, #6c757d 0%, #495057 100%);
  border-radius: 2px;
  transition: all 0.3s ease;
}

.resizer:hover .resizer-line {
  background: linear-gradient(180deg, #495057 0%, #343a40 100%);
}

.resizer.resizing .resizer-line {
  background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
}

/* 表单美化 */
.el-form-item__label {
  color: #495057;
  font-weight: 600;
}

/* 骨架屏美化 */
.el-skeleton__item {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #5a6fd8 0%, #6a4190 100%);
}

/* ===== 特殊组件美化 ===== */

/* 数字键盘区域美化 */
.digital-keyboard {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  color: white;
}

.digital-keyboard h3 {
  color: white !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.digital-keyboard p {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* 数字键盘按钮美化 */
.digital-keyboard button {
  background: rgba(255, 255, 255, 0.9) !important;
  border: none !important;
  color: #667eea !important;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.digital-keyboard button:hover:not(:disabled) {
  background: white !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.digital-keyboard button:active:not(:disabled) {
  transform: translateY(0);
}

.digital-keyboard button:disabled {
  background: rgba(255, 255, 255, 0.3) !important;
  color: rgba(255, 255, 255, 0.5) !important;
  cursor: not-allowed;
}

/* 选中状态的数字按钮 */
.digital-keyboard button.selected {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%) !important;
  color: #8b4513 !important;
  box-shadow: 0 0 20px rgba(252, 182, 159, 0.5);
}

/* 分数按钮美化 */
.score-buttons {
  gap: 8px;
}

/* 疵点编辑步骤高亮美化 */
.ring-2.ring-blue-500 {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border: 2px solid #667eea !important;
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.2);
  animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.2);
  }
  50% {
    box-shadow: 0 0 30px rgba(102, 126, 234, 0.4);
  }
}

/* 疵点记录按钮美化 */
.el-button:has-text("疵点记录") {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: white;
  border: none;
}

.el-button:has-text("同缸") {
  background: linear-gradient(135deg, #55a3ff 0%, #003d82 100%);
  color: white;
  border: none;
}

/* 上传区域美化 */
.el-form-item:has([secene="defect"]) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  padding: 16px;
  border: 2px dashed #dee2e6;
  transition: all 0.3s ease;
}

.el-form-item:has([secene="defect"]):hover {
  border-color: #667eea;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

/* 底部固定操作区域 */
.right-panel .flex-1.p-4.border-r .mt-4.pt-4 {
  // background-color: rgba(255, 255, 255, 0.95);
  position: sticky;
  bottom: 0;
  z-index: 10;
  margin-left: -16px;
  margin-right: -16px;
  padding-left: 16px;
  padding-right: 16px;
  box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.05);
}

/* 保存按钮特殊美化 */
.el-button:has-text("保存疵点") {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%) !important;
  border: none !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 12px 24px !important;
  border-radius: 25px !important;
  box-shadow: 0 6px 20px rgba(0, 184, 148, 0.3);
  transition: all 0.3s ease;
}

.el-button:has-text("保存疵点"):hover {
  background: linear-gradient(135deg, #00a085 0%, #008f76 100%) !important;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 184, 148, 0.4);
}

/* 复选框美化 */
.el-checkbox {
  font-weight: 500;
  color: #495057;
}

.el-checkbox__input.is-checked .el-checkbox__inner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
}

/* 响应式字体大小调整 */
@media screen and (max-width: 1366px) {
  .digital-keyboard h3 {
    font-size: 16px;
  }

  .digital-keyboard p {
    font-size: 12px;
  }

  .digital-keyboard button {
    font-size: 14px;
  }
}

@media screen and (min-width: 1920px) {
  .digital-keyboard h3 {
    font-size: 20px;
  }

  .digital-keyboard p {
    font-size: 16px;
  }

  .digital-keyboard button {
    font-size: 16px;
    height: 56px;
  }
}

/* ===== 动画效果 ===== */

/* 页面加载动画 */
.list-page {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片进入动画 */
.el-card {
  animation: slideInFromLeft 0.5s ease-out;
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 按钮点击波纹效果 */
.el-button {
  position: relative;
  overflow: hidden;
}

.el-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.el-button:active::after {
  width: 300px;
  height: 300px;
}

/* 疵点按钮特殊动画 */
.el-button[round] {
  animation: bounceIn 0.5s ease-out;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 数字键盘按钮点击动画 */
.digital-keyboard button:active {
  animation: buttonPress 0.1s ease-in-out;
}

@keyframes buttonPress {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

/* 表单验证错误动画 */
.el-form-item.is-error .el-input__wrapper {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

/* 成功提示动画 */
.el-message--success {
  animation: successPulse 0.5s ease-out;
}

@keyframes successPulse {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .list-page {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }

  .el-card {
    background: #34495e;
    color: #ecf0f1;
  }

  .digital-keyboard {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }

  .el-input__wrapper {
    background: #34495e;
    color: #ecf0f1;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .el-button {
    border: 2px solid currentColor;
  }

  .resizer {
    border: 3px solid #000;
  }

  .el-tabs__item {
    border: 2px solid currentColor;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>
