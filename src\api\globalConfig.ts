import { useRequest } from '@/use/useRequest'

// 获取全局配置列表
export function getGlobalConfigList() {
  return useRequest<Api.GlobalConfigList.Request, ResponseList<Api.GlobalConfigList.Response>>({
    url: '/admin/v1/globalConfig/list',
    method: 'get',
    pagination: true,
  })
}
// 批量更新全局配置
export function BatchUpdateGlobalConfigList() {
  return useRequest<Api.BatchUpdateGlobalConfigList.Request, ResponseList<Api.BatchUpdateGlobalConfigList.Response>>({
    url: '/admin/v1/globalConfig/list',
    method: 'put',
  })
}
// 获取全局配置表下拉列表
export function GetGlobalConfigDropdownList() {
  return useRequest<Api.GetGlobalConfigDropdownList.Request, ResponseList<Api.GetGlobalConfigDropdownList.Response>>({
    url: '/admin/v1/globalConfig/dropdownList',
    method: 'get',
  })
}

// 获取全局配置详情
export function getGlobalConfigDetail() {
  return useRequest<Api.GlobalConfig.Request, Api.GlobalConfig.Response>({
    url: '/admin/v1/globalConfig',
    method: 'get',
  })
}

// 创建全局配置
export function createGlobalConfig() {
  return useRequest<Api.GlobalConfigAdd.Request, Api.GlobalConfigAdd.Response>({
    url: '/admin/v1/globalConfig',
    method: 'post',
  })
}

// 更新全局配置
export function updateGlobalConfig() {
  return useRequest<Api.GlobalConfigAdd.Request, Api.GlobalConfigAdd.Response>({
    url: '/admin/v1/globalConfig',
    method: 'put',
  })
}

// 删除全局配置
export function deleteGlobalConfig() {
  return useRequest<{ id: number }, any>({
    url: '/admin/v1/globalConfig',
    method: 'delete',
  })
}
