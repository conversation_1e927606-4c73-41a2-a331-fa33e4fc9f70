// 婚姻状况
export const MaritalStatus: Record<string, string> = {
  1: '未婚',
  2: '已婚',
  3: '离异',
}

// 教育状况
export const EducationStatus: Record<string, string> = {
  1: '初高中',
  2: '大专',
  3: '本科',
  4: '研究生',
  5: '硕士',
}

// 资源类型
export const ResourceType: Record<number, string> = {
  1: '目录',
  2: '资源',
  3: '按钮',
}

// 默认结算类型
export const SettleType: Record<string, number> = {
  cashType: 1,
  dayType: 2,
  weekType: 3,
  moonType: 4,
}

// 仓库下拉
export const WarehouseTypeIdEnum: Record<string, number> = {
  finishProduction: 103, // 成品
  rawMaterial: 101, // 原料
  grey: 102, // 坯布
  commonWarehouseBin: 10013, // 公用仓位
}

// 往来单位类型下拉
export const BusinessUnitIdEnum: Record<string, number> = {
  knittingFactory: 11, // 织厂
  dyeFactory: 12, // 染整厂
  customer: 13, // 客户
  blankFabric: 14, // 坯布供应商
  finishedProduct: 15, // 成品供应商
  otherSupplier: 16, // 其他供应商
  colorCardSupplier: 17, // 色卡供应商
  logisticsSupplier: 18, // 物流供应商
  rawMaterial: 19, // 原料供应商
  dyeingMill: 20, //  染纱厂
  netFactory: 21, // 网厂
  twistFactory: 22, // 捻厂
}

// 员工分类
export const EmployeeType: Record<string, number> = {
  unknownPosition: 0, // 未知职务
  salesman: 20001, // 销售员
  biller: 20002, // 打单员
  porter: 20003, // 搬运工
  clothChecker: 20004, // 查布员
  warehouseManager: 20005, // 仓管员
  businessManager: 20006, // 业务经理
  follower: 20007, // 跟单员
  followerQC: 20008, // 跟单员qc
  driver: 20009, // 司机
  quarantine: 20011, // 质检员
  distributionCloth: 20010, // 配布员
  qualityInspectionSupervisor: 20014, // 质检主管
}

// 字典
export const DictionaryType: Record<string, number> = {
  fibre: 10003, // 纱线纤维
  duty: 10002, // 职责
  gender: 10001, // 性别
  weavingOrganization: 10005, // 织造组织
  craft: 10004, // 原料工艺
  width_unit: 10006, // 幅宽单位
  gram_weight_unit: 10007, // 克重单位
  bleach: 10009, // 漂染性
  value: 10008, // 用途
  colorKind: 10010, // 颜色类别
  deliveryType: 10011, // 交货方式
  PrinterSize: 10012, // 打印机尺寸
  commonWarehouseBin: 10013, // 公用仓位
  handFeel: 10014, // 手感
  hairEffect: 10015, // 抓毛效果
  scouringEffect: 10016, // 磨毛效果
  hairHead: 10017, // 布面毛头
  fabricTilt: 10018, // 布纹斜度
  colorLight: 10019, // 对色光源
  batchDifference: 10020, // 疋差
  positiveAndNegative: 10021, // 阴阳
  shrink: 10022, // 缩水率
  machineNumber: 10023, // 机台号
  defectKind: 10024, // 疵点种类
  clothLevel: 10025, // 坯布等级
}
export enum GetGfmCheckOrderTypeEnum {
  GfmCheckOrderTypeWaitDye = 1,
  GfmCheckOrderTypeDyeing = 2,
}

// 称重验布流程
export enum WeighingAndFabricInspectionProcessEnum {
  INSPECTION = 2, // 验布
  DROP_AND_INSPECTION = 1, // 称重+验布
}

export const WeighingAndFabricInspectionProcessLabels: Record<number, string> = {
  [WeighingAndFabricInspectionProcessEnum.INSPECTION]: '验布',
  [WeighingAndFabricInspectionProcessEnum.DROP_AND_INSPECTION]: '称重+验布',
}

// 织造入库方式
export enum WeavingStorageMethodEnum {
  SCAN_CODE = 1, // 扫码进仓
  BY_SHIFT = 2, // 按班次入库
  ONE_PERSON_ONE_ORDER = 3, // 一人一单
}

export const WeavingStorageMethodLabels: Record<number, string> = {
  [WeavingStorageMethodEnum.SCAN_CODE]: '扫码进仓',
  [WeavingStorageMethodEnum.BY_SHIFT]: '按班次入库',
  [WeavingStorageMethodEnum.ONE_PERSON_ONE_ORDER]: '一人一单',
}

// 布飞卷号规则
export enum BFSequenceNumberRuleEnum {
  PRODUCTION_ORDER = 3, // 生产单
  MACHINE = 2, // 机台
  PRODUCTION_ORDER_AND_MACHINE = 1, // 生产单+机台
}

export const BFSequenceNumberRuleLabels: Record<number, string> = {
  [BFSequenceNumberRuleEnum.PRODUCTION_ORDER]: '生产单',
  [BFSequenceNumberRuleEnum.MACHINE]: '机台',
  [BFSequenceNumberRuleEnum.PRODUCTION_ORDER_AND_MACHINE]: '生产单+机台',
}

// 全局枚举-后端设置的
export enum GlobalEnum {
  FpmQualityPassScore = 51301, // 质检合格分数
  MonthSettleDay = 51302, // 月结日
  ProductionNoticeCustomerSync = 51303, // 生产通知单客户配置
  StableValue = 51403, // 设置稳定值 文本
  AutoSave = 51404, // 是否自动保存
  AutoSaveSeconds = 51405, // 设置延后多少秒保存
  NoWeaverSelection = 51406, // 是否无需选择织工
  NoInspectorSelection = 51407, // 是否无需选择查布
  WeightReflection = 51408, // 是否重量反转
  DataHead = 51409, // 数据头 文本
  DataEnd = 51410, // 数据尾 文本
  ScanCodeReading = 51411, // 是否扫码后读数
  IsSyncToDefectData = 51412, // 是否同步到疵点资料（新增疵点资料）
}

// 坯布位数枚举
export enum DecimalPoint {
  DecimalPointOne = 1, // 一位小数
  DecimalPointTwo = 2, // 两位小数
  DecimalPointThree = 3, // 三位小数
  DecimalPointFour = 4, // 四位小数
}

export const DecimalPointLabels: Record<DecimalPoint, string> = {
  [DecimalPoint.DecimalPointOne]: '1位小数',
  [DecimalPoint.DecimalPointTwo]: '2位小数',
  [DecimalPoint.DecimalPointThree]: '3位小数',
  [DecimalPoint.DecimalPointFour]: '4位小数',
}

// 进位枚举
export enum Carry {
  CarryOne = 1, // 一进位
  CarryTwo = 2, // 两进位
  CarryThree = 3, // 三进位
  CarryFour = 4, // 四进位
  CarryRoundOff = 5, // 四舍五入
}

export const CarryLabels: Record<Carry, string> = {
  [Carry.CarryOne]: '逢1进位',
  [Carry.CarryTwo]: '逢2进位',
  [Carry.CarryThree]: '逢3进位',
  [Carry.CarryFour]: '逢4进位',
  [Carry.CarryRoundOff]: '四舍五入',
}
