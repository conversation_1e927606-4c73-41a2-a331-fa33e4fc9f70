import { useRequest } from '@/use/useRequest'
import { useDownLoad } from '@/use/useDownLoad'
// 员工列表
export function GetEmployeeListApi() {
  return useRequest({
    url: '/admin/v1/employee/list',
    method: 'get',
    pagination: true,
    pageSize: 50,
    // cache: true,
    // setExpireTime: 30000,
  })
}

// 员工列表--导出
export function GetEmployeeListApiExport({ nameFile }: any) {
  return useDownLoad({
    url: '/admin/v1/employee/list',
    method: 'get',
    nameFileTime: false,
    nameFile,
  })
}
// 删除员工
export function DeleteEmployeeApi() {
  return useRequest({
    url: '/admin/v1/employee',
    method: 'delete',
  })
}
// 更改状态
export function DisableEmployeeApi() {
  return useRequest({
    url: '/admin/v1/employee/updateStatus ',
    method: 'put',
  })
}
// 职责列表
// export const GetEmployeeDutyListApi = () => {
//   return useRequest({
//     url: '/admin/v1/employee/duty/list',
//     method: 'get',
//   })
// }
// 职责列表 (改为字典)
export function GetEmployeeDutyListApi() {
  return useRequest({
    url: '/admin/v1/dictionaryDetail/getDictionaryDetailEnumList',
    method: 'get',
  })
}

// 新建员工
export function PostEmployeeAddApi() {
  return useRequest({
    url: '/admin/v1/employee',
    method: 'post',
  })
}

// 更新员工
export function PutEmployeeAddApi() {
  return useRequest({
    url: '/admin/v1/employee',
    method: 'put',
  })
}

// 员工详情
export function GetEmployeeDetailApi() {
  return useRequest({
    url: '/admin/v1/employee/detail',
    method: 'get',
  })
}

// 婚姻状况列表
export function GetMaritalStatusListApi() {
  return useRequest({
    url: '/admin/v1/employee/marital_status/list',
    method: 'get',
  })
}

// 教育水平列表
export function GetEducationLevelListApi() {
  return useRequest({
    url: '/admin/v1/employee/education_level/list',
    method: 'get',
  })
}

// 获取部门树列表
export function GetDepartmentTreeListApi() {
  return useRequest({
    url: '/admin/v1/department/v2',
    method: 'get',
  })
}

// 字典列表
export function GetDictionaryDetailEnumListApi() {
  return useRequest<Api.GetDictionaryDetailEnumListApi.Request, Api.GetDictionaryDetailEnumListApi.Response>({
    url: '/admin/v1/dictionaryDetail/getDictionaryDetailEnumList',
    method: 'get',
  })
}
